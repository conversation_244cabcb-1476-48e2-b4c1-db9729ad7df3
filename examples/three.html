<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LCC ThreeJS Demo</title>
</head>
<style>
    html,
    body {
        margin: 0;
        padding: 0;
        height: 100%;
        overflow: hidden;
    }

    canvas {
        display: block;
    }

    /* Performance Monitor Styles */
    #performance-monitor {
        position: fixed;
        bottom: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: #00ff00;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #333;
        min-width: 200px;
        z-index: 1000;
    }

    #performance-monitor .title {
        color: #ffffff;
        font-weight: bold;
        margin-bottom: 5px;
        border-bottom: 1px solid #333;
        padding-bottom: 3px;
    }

    #performance-monitor .metric {
        margin: 3px 0;
        display: flex;
        justify-content: space-between;
    }

    #performance-monitor .metric .label {
        color: #cccccc;
    }

    #performance-monitor .metric .value {
        color: #00ff00;
        font-weight: bold;
    }
</style>

<body>
    <!-- Performance Monitor -->
    <div id="performance-monitor">
        <div class="title">Performance Monitor</div>
        <div class="metric">
            <span class="label">FPS:</span>
            <span class="value" id="fps-value">0</span>
        </div>
        <div class="metric">
            <span class="label">Used Heap:</span>
            <span class="value" id="used-heap-value">0 MB</span>
        </div>
        <div class="metric">
            <span class="label">Total Heap:</span>
            <span class="value" id="total-heap-value">0 MB</span>
        </div>
        <div class="metric">
            <span class="label">Heap Limit:</span>
            <span class="value" id="heap-limit-value">0 MB</span>
        </div>
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "./engine/three/three.module.js",
                "three/addons/": "./engine/three/jsm/"
            }
        }
    </script>
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { FirstPersonControls } from './engine/three/FirstPersonControls.js';
        import { LCCRender } from '../sdk/lcc-0.5.2.js'

        const scene = new THREE.Scene();

        const renderer = new THREE.WebGLRenderer();
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);

        renderer.setClearColor(0x000000);
        document.body.appendChild(renderer.domElement);

        const camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 150000);
        // camera.up.set(0, 0, 1);  // Use modelMatrix instead
        camera.position.set(0, 2, 0);

        const clock = new THREE.Clock();

        // Performance monitoring variables
        let frameCount = 0;
        let lastTime = performance.now();
        let fps = 0;
        const fpsUpdateInterval = 1000; // Update FPS every 1 second

        // const controls = new OrbitControls(camera, renderer.domElement);
        // controls.target.set( 0, 2, 1 );
        // controls.update();
        // controls.enabled = false

        const firstPersonControl = new FirstPersonControls(camera, renderer.domElement)
        firstPersonControl.enabled = true // true
        firstPersonControl.movementSpeed = 5
        firstPersonControl.lookAt(new THREE.Vector3(0, 2, 1))

        // Use the model matrix to change the model coordinate system, or set the up direction of the camera
        // eg.  camera.up.set(0, 0, 1);
        const modelMatrix = new THREE.Matrix4(
            -1, 0, 0, 0,
            0, 0, 1, 0,
            0, 1, 0, 0,
            0, 0, 0, 1
        );

        // Loading lcc object
        const lccObj = LCCRender.load({
            camera: camera,
            scene: scene,
            dataPath: `${location.origin}/assets/bamboo_temple_ply/meta.lcc`, // Lcc data path
            // `${location.origin}/assets/ConfuciusTemple/meta.lcc`
            renderLib: THREE,
            canvas: renderer.domElement,
            renderer: renderer,
            useEnv: true,
            useIndexDB: true,  // Enable or disable data caching
            useLoadingEffect: true, // Enable or disable loading effects
            modelMatrix: modelMatrix, // Set model matrix
            appKey: 'replace_your_key'
        }, (mesh) => {
            console.log('Lcc object Loaded:  ', mesh);
            const lods = lccObj.getLodInfos()
            console.log('lod levels: '+JSON.stringify(lods))
            const ret = lccObj.hasShcoef();
            console.log('has shcoef: '+ret)
        }, (percent) => {
            console.log('Lcc object loading: ' + (percent * 100).toFixed(1) + '%');
        }, () => {
            console.log("Lcc object loading failure");
        });

        lccObj.setLodAutoLevelUp(true);   // 开启空地一体化Lod策略

        window.lccObj = lccObj;
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Performance monitoring functions
        function updatePerformanceMonitor() {
            frameCount++;
            const currentTime = performance.now();

            // Update FPS every second
            if (currentTime - lastTime >= fpsUpdateInterval) {
                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;

                // Update FPS display
                document.getElementById('fps-value').textContent = fps;
            }

            // Update memory usage if available
            if (performance.memory) {
                const memory = performance.memory;

                // Helper function to format memory size
                function formatMemorySize(bytes) {
                    const mb = bytes / 1024 / 1024;
                    if (mb >= 1024) {
                        return (mb / 1024).toFixed(2) + ' GB';
                    } else {
                        return Math.round(mb) + ' MB';
                    }
                }

                document.getElementById('used-heap-value').textContent = formatMemorySize(memory.usedJSHeapSize);
                document.getElementById('total-heap-value').textContent = formatMemorySize(memory.totalJSHeapSize);
                document.getElementById('heap-limit-value').textContent = formatMemorySize(memory.jsHeapSizeLimit);
            } else {
                // Fallback for browsers that don't support performance.memory
                document.getElementById('used-heap-value').textContent = 'N/A';
                document.getElementById('total-heap-value').textContent = 'N/A';
                document.getElementById('heap-limit-value').textContent = 'N/A';
            }
        }

        function render() {
            if (firstPersonControl?.enabled) {
                firstPersonControl?.update(clock.getDelta())
            }
            LCCRender.update();
            renderer.render(scene, camera);

            // Update performance monitor
            updatePerformanceMonitor();
        }

        function animate() {
            renderer.setAnimationLoop(() => {
                render();
            });
        }

        animate();
    </script>
</body>

</html>