<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Use correct character set. -->
  <meta charset="utf-8" />
  <!-- Tell <PERSON> to use the latest, best version. -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <!-- Make the application on mobile take up the full browser screen and disable user scaling. -->
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />
  <title>LCC CesiumJS Demo</title>
  <script src="./engine/cesium/Cesium.js"></script>
  <style>
    @import url(./engine/cesium/widgets.css);

    html,
    body,
    #cesiumContainer {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }
  </style>
</head>

<body>
  <div id="cesiumContainer"></div>
  <script type="module">
    import { LCCRender } from '../sdk/lcc-0.5.2.js'
    // Maybe you need to create an access token of your own.  https://cesium.com/learn/ion/cesium-ion-access-tokens/
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIyMWM0ZmE4MS1kMzJiLTRmZjctYjUwYi1mZGY3YTU4NmEzN2UiLCJpZCI6MzE1ODExLCJpYXQiOjE3NTA5MTYyNjJ9.BrvyhHFjpehzlJd8WZNM5ku1e9VMEOydFDohhLjRrs4'

    const viewer = new Cesium.Viewer("cesiumContainer", {
      orderIndependentTranslucency: false, // Close order-independent rendering
      useDefaultRenderLoop: true,
      resolutionScale: window.devicePixelRatio,
    });

    viewer.scene.debugShowFramesPerSecond = true

    // Get the global coordinates of the local coordinate origin
    const cartesian3 = Cesium.Cartesian3.fromDegrees(114.0592, 22.5429, 60);
    // The local coordinate z-axis is perpendicular to the ground, and the local coordinate y-axis points to the north.
    const m1 = Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Cartesian3.fromDegrees(114.0592, 22.5429, 10));

    const lccObj = LCCRender.load({
      camera: viewer.camera,
      scene: viewer.scene,
      dataPath: `${location.origin}/assets/Room_tour_L2_PRO_P/meta.lcc`, // Lcc data path
      renderLib: Cesium,
      canvas: viewer.scene.canvas,
      useEnv: false,
      modelMatrix: m1, // Init matrix
      useIndexDB: true,  // Enable or disable data caching
      appKey: 'replace_your_key'
    }, (mesh) => {  
      console.log("Lcc object loaded: ", mesh);

      let cartesian = lccObj.getTranslation();

      console.log("cartesian: ", cartesian)

      let point = {
        position: cartesian,
        point: { pixelSize: 2, color: Cesium.Color.RED }
      }

      const entity = viewer.entities.add(point)

      viewer.flyTo(entity)

    }, (percent) => {
      console.log("Lcc object loading: " + (percent * 100).toFixed(1) + "%");
    }, () => {
      console.log("Lcc object loading failure");
    });

    window.lccObj = lccObj

  </script>
</body>

</html>