<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FirstPersonControls Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .control-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .control-item h3 {
            margin-top: 0;
            color: #333;
        }
        .desktop { border-left: 4px solid #007bff; }
        .mobile { border-left: 4px solid #28a745; }
        .feature { border-left: 4px solid #ffc107; }
        
        .test-button {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 15px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🎮 FirstPersonControls 测试页面</h1>
        <p>这个页面用于测试FirstPersonControls的新功能，包括右键平移和移动端触摸支持。</p>
        <a href="examples/three.html" class="test-button">🚀 打开主测试页面</a>
    </div>

    <div class="controls-list">
        <div class="control-item desktop">
            <h3>🖱️ 桌面端控制</h3>
            <ul>
                <li><strong>左键拖拽</strong>: 旋转视角</li>
                <li><strong>右键拖拽</strong>: 平移位置</li>
                <li><strong>WASD键</strong>: 前后左右移动</li>
                <li><strong>R/F键</strong>: 上下移动</li>
            </ul>
        </div>

        <div class="control-item mobile">
            <h3>📱 移动端控制</h3>
            <ul>
                <li><strong>单指拖拽</strong>: 旋转视角</li>
                <li><strong>双指拖拽</strong>: 平移位置</li>
                <li><strong>自动检测</strong>: 移动设备显示提示</li>
                <li><strong>手势切换</strong>: 无缝切换操作模式</li>
            </ul>
        </div>

        <div class="control-item feature">
            <h3>📊 性能监控</h3>
            <ul>
                <li><strong>FPS显示</strong>: 实时帧率</li>
                <li><strong>内存监控</strong>: JS堆内存使用情况</li>
                <li><strong>智能单位</strong>: 自动MB/GB切换</li>
                <li><strong>实时更新</strong>: 每秒刷新数据</li>
            </ul>
        </div>

        <div class="control-item">
            <h3>🔧 配置选项</h3>
            <ul>
                <li><code>enablePan</code>: 启用右键平移</li>
                <li><code>panSpeed</code>: 平移速度</li>
                <li><code>enableTouch</code>: 启用触摸支持</li>
                <li><code>touchSensitivity</code>: 触摸灵敏度</li>
            </ul>
        </div>
    </div>

    <div class="test-info">
        <h2>🧪 测试步骤</h2>
        <ol>
            <li>点击上方按钮打开主测试页面</li>
            <li>观察左下角的性能监控面板</li>
            <li>测试鼠标左键拖拽旋转功能</li>
            <li>测试鼠标右键拖拽平移功能</li>
            <li>在移动设备上测试触摸操作</li>
            <li>检查内存显示单位是否正确</li>
        </ol>
        
        <h2>🐛 问题报告</h2>
        <p>如果发现任何问题，请检查浏览器控制台是否有错误信息。</p>
    </div>

    <script>
        // 检测设备类型并显示相应信息
        function detectDevice() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                           (window.innerWidth <= 768 && 'ontouchstart' in window);
            
            if (isMobile) {
                document.body.style.background = '#e8f5e8';
                const mobileItems = document.querySelectorAll('.mobile');
                mobileItems.forEach(item => {
                    item.style.borderLeftColor = '#28a745';
                    item.style.borderLeftWidth = '6px';
                });
            }
        }

        detectDevice();
        window.addEventListener('resize', detectDevice);
    </script>
</body>
</html>
