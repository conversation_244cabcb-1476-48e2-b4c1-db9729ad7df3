# 性能监控和右键位移功能

## 新增功能

### 1. 性能监控窗口
在页面左下角添加了一个性能监控面板，显示以下信息：
- **FPS**: 实时帧率显示
- **Used Heap**: 已使用的JavaScript堆内存 (MB)
- **Total Heap**: 总JavaScript堆内存 (MB)
- **Heap Limit**: JavaScript堆内存限制 (MB)

#### 特性：
- 固定在左下角，不会遮挡主要内容
- 黑色半透明背景，绿色文字，类似终端风格
- 每秒更新一次FPS数据
- 实时显示内存使用情况
- 对于不支持`performance.memory`的浏览器显示"N/A"

### 2. FirstPersonControls右键位移功能
为FirstPersonControls添加了右键拖拽平移功能：

#### 桌面端操作：
- **左键拖拽**: 旋转视角
- **右键拖拽**: 平移位置
  - 水平拖拽：左右平移
  - 垂直拖拽：上下平移

#### 移动端操作：
- **单指触摸拖拽**: 旋转视角
- **双指触摸拖拽**: 平移位置
  - 水平拖拽：左右平移
  - 垂直拖拽：上下平移

#### 配置选项：
- `enablePan`: 启用/禁用右键平移功能（默认：true）
- `panSpeed`: 平移速度倍数（默认：1.0）
- `enableTouch`: 启用/禁用触摸支持（默认：true）
- `touchSensitivity`: 触摸旋转灵敏度（默认：1.0）
- `touchPanSensitivity`: 触摸平移灵敏度（默认：0.5）

#### 键盘控制（原有功能保持不变）：
- W/↑: 前进
- S/↓: 后退
- A/←: 左移
- D/→: 右移
- R: 上升
- F: 下降

## 技术实现

### 性能监控
- 使用`performance.now()`计算FPS
- 使用`performance.memory` API获取内存信息
- 每帧更新显示，FPS每秒计算一次

### 右键位移
- 区分鼠标左键（button === 0）和右键（button === 2）
- 添加`isPanning`状态管理
- 在`update()`方法中处理平移逻辑
- 使用`translateX()`和`translateY()`实现位移

## 使用方法

1. 打开`examples/three.html`
2. 性能监控面板会自动显示在左下角
3. 使用鼠标左键拖拽旋转视角
4. 使用鼠标右键拖拽平移位置
5. 使用WASD键进行移动

## 最新更新 (v2.0)

### 内存显示优化
- 当内存使用超过1024MB时，自动显示为GB单位
- 例如：1536MB → 1.50 GB
- 保持2位小数精度

### 右键位移功能修复
- 完全重写了右键拖拽平移逻辑
- 修复了平移不工作的问题
- 优化了平移灵敏度和响应性
- 改进了坐标系变换计算

### 🆕 移动端触摸支持
- **单指触摸**：旋转视角（类似鼠标左键）
- **双指触摸**：平移位置（类似鼠标右键）
- 自动检测移动设备并显示触摸控制提示
- 针对移动端优化的触摸灵敏度设置
- 支持多点触控手势切换

## 浏览器兼容性

- 性能监控：支持所有现代浏览器，Chrome/Edge中内存信息最完整
- 右键位移：支持所有支持Pointer Events的现代浏览器

## 测试方法

### 桌面端测试
1. 打开`examples/three.html`
2. 观察左下角性能监控面板
3. 测试鼠标操作：
   - **左键拖拽**：应该能旋转视角
   - **右键拖拽**：应该能平移位置（左右/上下移动）
4. 检查内存显示单位是否正确（MB/GB）

### 移动端测试
1. 在移动设备上打开`examples/three.html`
2. 应该看到左上角的触摸控制提示
3. 测试触摸操作：
   - **单指拖拽**：应该能旋转视角
   - **双指拖拽**：应该能平移位置
4. 测试手势切换：从双指切换到单指应该无缝切换操作模式
